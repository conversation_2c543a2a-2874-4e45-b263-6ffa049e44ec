# SSE 解析问题解决方案

## 问题分析

通过浏览器测试发现，当前的聊天功能无法正常工作，主要问题是：

1. **SSE 格式解析错误**：Python 后端返回的是标准 SSE 格式 `data: {JSON对象}`，但 AG-UI HttpAgent 期望的是纯 JSON 格式
2. **错误信息**：`Unexpected token 'd', "data: {"ty"... is not valid JSON`
3. **根本原因**：前端的 `YaiNexusServiceAdapter.process()` 方法中，HttpAgent 直接处理 SSE 流，但没有正确解析 SSE 格式

## 当前架构流程

```
用户消息 → CopilotKit → YaiNexusServiceAdapter.process() → HttpAgent.run() → Python /agui 端点
                                                                ↓
                                                        SSE: "data: {JSON}"
                                                                ↓
                                                        AG-UI Client 尝试 JSON.parse()
                                                                ↓
                                                            解析失败 ❌
```

## 解决方案：在前端 process 方法中处理 SSE 解析

### 方案概述

在 `YaiNexusServiceAdapter.process()` 方法中，添加 SSE 解析层，将 Python 后端返回的 SSE 格式转换为 AG-UI 期望的纯 JSON 格式。

### 具体实现

#### 1. 创建 SSE 解析工具函数

```typescript
// packages/fekit/src/sse-parser.ts
export interface SSEEvent {
  event?: string;
  data?: string;
  id?: string;
  retry?: number;
}

export function parseSSEStream(sseText: string): SSEEvent[] {
  const events: SSEEvent[] = [];
  const lines = sseText.split('\n');
  let currentEvent: Partial<SSEEvent> = {};

  for (const line of lines) {
    if (line.trim() === '') {
      // 空行表示事件结束
      if (currentEvent.data) {
        events.push(currentEvent as SSEEvent);
      }
      currentEvent = {};
      continue;
    }

    const colonIndex = line.indexOf(':');
    if (colonIndex === -1) continue;

    const field = line.substring(0, colonIndex).trim();
    const value = line.substring(colonIndex + 1).trim();

    switch (field) {
      case 'data':
        currentEvent.data = value;
        break;
      case 'event':
        currentEvent.event = value;
        break;
      case 'id':
        currentEvent.id = value;
        break;
      case 'retry':
        currentEvent.retry = parseInt(value, 10);
        break;
    }
  }

  return events;
}

export function parseAGUIFromSSE(sseText: string): any[] {
  const events = parseSSEStream(sseText);
  const aguiEvents: any[] = [];

  for (const event of events) {
    if (event.data) {
      try {
        const aguiEvent = JSON.parse(event.data);
        aguiEvents.push(aguiEvent);
      } catch (error) {
        console.warn('Failed to parse AG-UI event from SSE data:', event.data);
      }
    }
  }

  return aguiEvents;
}
```

#### 2. 修改 YaiNexusServiceAdapter.process() 方法

```typescript
// packages/fekit/src/handler.ts
import { parseAGUIFromSSE } from './sse-parser';

class YaiNexusServiceAdapter implements CopilotServiceAdapter {
  // ... 现有代码 ...

  async process(
    request: CopilotRuntimeChatCompletionRequest
  ): Promise<CopilotRuntimeChatCompletionResponse> {
    // ... 现有的初始化代码 ...

    try {
      // 准备 AG-UI 格式的输入
      const agentInput = {
        threadId,
        runId,
        messages: formattedMessages,
        tools: request.actions || [],
        context: [],
        state: null,
        forwardedProps: request.forwardedParameters || {},
      };

      // 方案：直接获取 SSE 流并解析为 AG-UI 事件
      try {
        // 使用 HttpAgent 获取原始 SSE 流
        const events$ = this.httpAgent.run(agentInput);

        requestLogger.info("Starting SSE stream processing");

        // 收集所有 SSE 数据
        const sseChunks: string[] = [];
        
        await new Promise<void>((resolve, reject) => {
          events$.subscribe({
            next: (chunk: any) => {
              // HttpAgent 可能返回不同格式的数据，需要适配
              if (typeof chunk === 'string') {
                sseChunks.push(chunk);
              } else if (chunk && typeof chunk.data === 'string') {
                sseChunks.push(chunk.data);
              }
            },
            complete: () => {
              requestLogger.info("SSE stream completed", { 
                chunkCount: sseChunks.length 
              });
              resolve();
            },
            error: (error) => {
              requestLogger.error("SSE stream error", { error });
              reject(error);
            }
          });
        });

        // 解析 SSE 数据为 AG-UI 事件
        const fullSSEText = sseChunks.join('');
        const aguiEvents = parseAGUIFromSSE(fullSSEText);

        requestLogger.info("Parsed AG-UI events from SSE", { 
          eventCount: aguiEvents.length 
        });

        // 如果有 eventSource，将解析后的事件传递给它
        if (request.eventSource && typeof request.eventSource.stream === 'function') {
          // 将 AG-UI 事件转换为 CopilotKit 期望的格式
          for (const aguiEvent of aguiEvents) {
            await request.eventSource.stream(aguiEvent);
          }
        }

        // 返回成功响应
        return {
          threadId,
          runId,
          // 可以包含最后一条消息或摘要信息
          messages: aguiEvents.length > 0 ? [aguiEvents[aguiEvents.length - 1]] : [],
        };

      } catch (streamError) {
        requestLogger.error("Failed to process SSE stream", {
          error: streamError instanceof Error ? streamError.message : String(streamError),
        });
        throw streamError;
      }

    } catch (error) {
      requestLogger.error("Error in process method", {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
}
```

### 优势

1. **解决核心问题**：直接处理 SSE 格式解析，避免 AG-UI Client 的 JSON 解析错误
2. **保持架构简洁**：在现有的 process 方法中添加解析层，不需要大幅重构
3. **类型安全**：使用 TypeScript 确保数据格式正确
4. **错误处理**：完善的错误处理和日志记录
5. **向后兼容**：不影响现有的 CopilotKit 集成

### 实施步骤

1. 创建 `packages/fekit/src/sse-parser.ts` 文件
2. 修改 `packages/fekit/src/handler.ts` 中的 `process` 方法
3. 添加相应的类型定义和错误处理
4. 测试 SSE 解析功能
5. 验证聊天功能是否正常工作

### 风险评估

- **低风险**：只修改前端解析逻辑，不影响后端
- **可回滚**：如果出现问题，可以快速回滚到当前版本
- **测试友好**：可以单独测试 SSE 解析功能

## 请求批准

这个方案可以解决当前的 SSE 解析问题，使聊天功能正常工作。请批准实施此方案。
